<template>
  <div class="base-field-config-dialog">
    <el-dialog
      class="JustMake-dialog"
      title="配置基础资料栏位"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :before-close="handleClose"
      :modal-append-to-body="false"
      :append-to-body="true"
      top="18vh"
      width="550px"
    >
      <div class="dialog-content">
        <!-- 上部分：基础表选择和搜索 -->
        <div class="top-section">
          <el-form :inline="true" :model="searchForm" >
            <el-form-item label="基础资料" prop="table" style="margin-bottom: 0;">
              <el-select
                v-model="searchForm.table"
                placeholder="请选择表类型"
                @change="handleTableTypeChange"
                style="width: 200px"
              >
                <el-option
                  v-for="table in tableOptions"
                  :key="table.name"
                  :label="getTableOptionLabel(table)"
                  :value="table.name"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="keyword" style="width: auto; margin-bottom: 0;">
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索栏位代号或名称"
                prefix-icon="el-icon-search"
                clearable
                @input="handleSearch"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>

        <!-- 下部分：表格 -->
        <div class="table-section">
          <vxe-grid
            ref="fieldGridRef"
            class="vxe-grid-common"
            v-bind="fieldGridOptions"
          >
            <!-- 栏位代号 -->
            <template #field_code_default="{ row }">
              <span>{{ getFieldCodeDisplay(row.field) }}</span>
            </template>

            <!-- 启用开关 -->
            <template #show_switch_default="{ row }">
              <div class="switch-cell">
                <el-switch
                  v-model="row.is_show"
                  active-value="T"
                  inactive-value="F"
                  size="mini"
                  active-color="#08b68b"
                  inactive-color="#dcdfe6"
                  @change="handleShowChange(row)"
                ></el-switch>
              </div>
            </template>
          </vxe-grid>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getBasicDict } from '@/api/admin/base'

export default {
  name: 'BaseFieldConfigDialog',
  props: {
    // 当前已存在的栏位设置
    existingColumns: {
      type: Array,
      default: () => []
    },
    FUNID:{
      type: String,
      required: true
    },
    MODULEValue:{
      type: String,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchForm: {
        table: '',
        keyword: ''
      },
      tableOptions: [], // 将从接口动态加载
      fieldData: [], // 字段数据
      originalFieldData: [], // 原始字段数据
      existingFieldsMap: null, // 记录已存在字段的映射
      fieldGridOptions: {
        border: true,
        stripe: true,
        showOverflow: true,
        showHeaderOverflow: true,
        rowConfig: { isCurrent: true },
        height: '500px',
        loading: false,
        headerAlign: 'center',
        columns: [
          { type: 'seq', width: 60, title: '项次', align: 'center' },
          {
            field: 'field',
            title: '栏位代号',
            width: 200,
            slots: { default: 'field_code_default' }
          },
          { field: 'title', title: '栏位名称' },
          {
            field: 'is_show',
            width: 80,
            title: '添加',
            slots: { default: 'show_switch_default' }
          },
        ],
        data: []
      }
    }
  },

  methods: {
    // 获取表类型选项的显示标签
    getTableOptionLabel(table) {
      return `${table.remark}（${table.name}）`
    },

    // 获取栏位代号的显示格式
    getFieldCodeDisplay(fieldCode) {
      return `${this.searchForm.table}.${fieldCode}`
    },

    // 显示对话框
    async show() {
      this.dialogVisible = true
      this.resetData()
      // 先加载表选项
      await this.loadTableOptions()
      // 默认选择第一个表类型
      if (this.tableOptions.length > 0) {
        this.searchForm.table = this.tableOptions[0].name
        this.loadFieldData()
      }
    },

    // 重置数据
    resetData() {
      this.searchForm = {
        table: '',
        keyword: ''
      }
      this.fieldData = []
      this.originalFieldData = []
      this.fieldGridOptions.data = []
      this.existingFieldsMap = new Map()
    },

    // 加载表选项
    async loadTableOptions() {
      try {
        const response = await getBasicDict({FUNID: this.FUNID}, this.MODULEValue)
        if (response && response.data && Array.isArray(response.data)) {
          this.tableOptions = response.data
        }
      } catch (error) {
        this.requestFailed(error)
      }
    },

    // 表类型变更
    handleTableTypeChange() {
      if (this.searchForm.table) {
        this.loadFieldData()
      }
    },

    // 加载字段数据
    loadFieldData() {
      try {
        this.fieldGridOptions.loading = true

        // 从已加载的表选项中找到对应的表数据
        const selectedTable = this.tableOptions.find(table => table.name === this.searchForm.table)
        if (selectedTable && selectedTable.fields && Array.isArray(selectedTable.fields)) {
          // 转换数据格式
          this.fieldData = selectedTable.fields.map(field => ({
            field: field.name || field.field || '',
            title: field.label || field.name || '',
            is_show: 'F' // 默认不显示
          }))
        } else {
          // 如果没有找到对应的表数据，使用空数组
          this.fieldData = []
        }

        // 检查并标记已存在的字段
        this.markExistingFields()

        this.originalFieldData = [...this.fieldData]
        this.handleSearch() // 应用搜索过滤

      } catch (error) {
        console.error(error)
      } finally {
        this.fieldGridOptions.loading = false
      }
    },



    // 标记已存在的字段并默认选中
    markExistingFields() {
      // 初始化并清空之前的记录
      if (!this.existingFieldsMap) {
        this.existingFieldsMap = new Map()
      } else {
        this.existingFieldsMap.clear()
      }

      if (!this.existingColumns || this.existingColumns.length === 0) {
        return
      }

      // 获取已存在栏位的字段名列表
      const existingFieldNames = this.existingColumns.map(col => col.field)

      // 如果字段已存在，默认启用（基于格式化后的field进行匹配）
      this.fieldData.forEach(field => {
        const formattedFieldCode = this.getFieldCodeDisplay(field.field)
        if (existingFieldNames.includes(formattedFieldCode)) {
          field.is_show = 'T'
          // 记录已存在的字段及其初始状态
          this.existingFieldsMap.set(formattedFieldCode, {
            originalField: field.field,
            formattedField: formattedFieldCode,
            initialShowState: 'T'
          })
        }
      })
    },

    // 搜索处理
    handleSearch() {
      if (!this.searchForm.keyword || this.searchForm.keyword.trim() === '') {
        this.fieldGridOptions.data = [...this.originalFieldData]
      } else {
        const keyword = this.searchForm.keyword.trim().toLowerCase()
        this.fieldGridOptions.data = this.originalFieldData.filter(field => {
          const fieldName = (field.field || '').toLowerCase()
          const fieldTitle = (field.title || '').toLowerCase()
          return fieldName.includes(keyword) || fieldTitle.includes(keyword)
        })
      }
    },
    // 显示状态变更
    handleShowChange(row) {
      console.log('字段显示状态变更:', row.field, row.is_show)
      
      // 更新原始数据
      const originalRow = this.originalFieldData.find(item => item.field === row.field)
      if (originalRow) {
        originalRow.is_show = row.is_show
      }
    },

    // 确认
    handleConfirm() {
      const changeResult = this.analyzeFieldChanges()

      this.$emit('confirm', changeResult)
      this.handleClose()
    },

    // 分析字段变更
    analyzeFieldChanges() {
      // 确保Map已初始化
      if (!this.existingFieldsMap) {
        this.existingFieldsMap = new Map()
      }

      const enabledFields = []  // 启用的字段（需要添加到栏位设置）
      const disabledFields = [] // 禁用的字段（需要从栏位设置移除）

      // 遍历当前所有字段数据
      this.fieldData.forEach(field => {
        const formattedFieldCode = this.getFieldCodeDisplay(field.field)
        const isExisting = this.existingFieldsMap.has(formattedFieldCode)

        if (field.is_show === 'T') {
          // 启用的字段
          if (!isExisting) {
            // 新启用的字段
            enabledFields.push({
              field: formattedFieldCode,
              title: field.title,
              is_show: field.is_show,
              isNew: true
            })
          }
          // 已存在且仍然启用的字段不需要处理
        } else {
          // 移除的字段
          if (isExisting) {
            // 之前存在但现在被移除的字段
            disabledFields.push({
              field: formattedFieldCode,
              title: field.title,
              is_show: field.is_show,
              wasExisting: true
            })
          }
          // 新字段且未启用的不需要处理
        }
      })

      return {
        enabledFields,
        disabledFields
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.resetData()
    }
  }
}
</script>

<style lang="less" scoped>
// 输入框高度调整
::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}
::v-deep  .el-input__icon {
  line-height: 32px;
}

.base-field-config-dialog {
  .dialog-content {
    display: flex;
    flex-direction: column;
    height: 500px;
  }

  .search-form {
    margin: 0;
    
    .el-form-item {
      margin-bottom: 0;
    }
  }

  .table-section {
    flex: 1;
    overflow: hidden;
  }

  .switch-cell {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
